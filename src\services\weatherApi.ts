import axios from 'axios';
import {
  CurrentWeather,
  HourlyForecast,
  DailyForecast,
  LocationData,
  TemperatureUnit,
} from '../types/weather';

const API_KEY = import.meta.env.VITE_OPENWEATHER_API_KEY;
const BASE_URL = 'https://api.openweathermap.org/data/2.5';
const GEO_URL = 'https://api.openweathermap.org/geo/1.0';

if (!API_KEY) {
  console.warn('OpenWeatherMap API key not found. Please add VITE_OPENWEATHER_API_KEY to your .env file.');
}

class WeatherApiService {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Get current weather by coordinates
   */
  async getCurrentWeather(lat: number, lon: number, units: TemperatureUnit = 'metric'): Promise<CurrentWeather> {
    try {
      const response = await axios.get(`${BASE_URL}/weather`, {
        params: {
          lat,
          lon,
          appid: this.apiKey,
          units,
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get current weather by city name
   */
  async getCurrentWeatherByCity(city: string, units: TemperatureUnit = 'metric'): Promise<CurrentWeather> {
    try {
      const response = await axios.get(`${BASE_URL}/weather`, {
        params: {
          q: city,
          appid: this.apiKey,
          units,
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get 5-day hourly forecast
   */
  async getHourlyForecast(lat: number, lon: number, units: TemperatureUnit = 'metric'): Promise<HourlyForecast> {
    try {
      const response = await axios.get(`${BASE_URL}/forecast`, {
        params: {
          lat,
          lon,
          appid: this.apiKey,
          units,
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get 8-day daily forecast (requires One Call API)
   */
  async getDailyForecast(lat: number, lon: number, units: TemperatureUnit = 'metric'): Promise<DailyForecast> {
    try {
      const response = await axios.get(`${BASE_URL}/onecall`, {
        params: {
          lat,
          lon,
          appid: this.apiKey,
          units,
          exclude: 'minutely,alerts',
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Search for locations by name
   */
  async searchLocations(query: string, limit: number = 5): Promise<LocationData[]> {
    try {
      const response = await axios.get(`${GEO_URL}/direct`, {
        params: {
          q: query,
          limit,
          appid: this.apiKey,
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get location by coordinates (reverse geocoding)
   */
  async getLocationByCoords(lat: number, lon: number): Promise<LocationData[]> {
    try {
      const response = await axios.get(`${GEO_URL}/reverse`, {
        params: {
          lat,
          lon,
          limit: 1,
          appid: this.apiKey,
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get comprehensive weather data for a location
   */
  async getCompleteWeatherData(lat: number, lon: number, units: TemperatureUnit = 'metric') {
    try {
      const [currentWeather, hourlyForecast, location] = await Promise.all([
        this.getCurrentWeather(lat, lon, units),
        this.getHourlyForecast(lat, lon, units),
        this.getLocationByCoords(lat, lon),
      ]);

      return {
        currentWeather,
        hourlyForecast,
        location: location[0] || null,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  private handleError(error: any): Error {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const message = error.response?.data?.message || error.message;

      switch (status) {
        case 401:
          return new Error('Invalid API key. Please check your OpenWeatherMap API key.');
        case 404:
          return new Error('Location not found. Please check the city name or coordinates.');
        case 429:
          return new Error('API rate limit exceeded. Please try again later.');
        case 500:
          return new Error('Weather service is temporarily unavailable. Please try again later.');
        default:
          return new Error(`Weather API error: ${message}`);
      }
    }

    return new Error('An unexpected error occurred while fetching weather data.');
  }
}

// Create and export a singleton instance
export const weatherApi = new WeatherApiService(API_KEY || '');
export default weatherApi;
