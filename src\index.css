@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Weather background animations */
.weather-bg-sunny {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.weather-bg-cloudy {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.weather-bg-rainy {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

.weather-bg-snowy {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
}

.weather-bg-stormy {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

.weather-bg-night {
  background: linear-gradient(135deg, #1e3a8a 0%, #312e81 100%);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
